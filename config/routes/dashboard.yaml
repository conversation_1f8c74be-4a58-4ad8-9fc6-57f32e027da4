# Dashboard routes configuration
# Migrated from Zend Framework routing

dashboard_index:
    path: /dashboard
    controller: Sparefoot\MyFootService\Controller\DashboardController::index
    methods: [GET]
    
# Alternative route for root redirect to dashboard (if needed)
dashboard_home:
    path: /
    controller: Sparefoot\MyFootService\Controller\DashboardController::index
    methods: [GET]
    condition: "request.headers.get('host') matches '/myfoot/'"
