<?php

namespace Sparefoot\MyFootService\Controller;

use Sparefoot\MyFootService\Service\User;
use Sparefoot\MyFootService\Service\Account;
use Sparefoot\MyFootService\Service\Facility;
use Sparefoot\MyFootService\Models\Features;
use Sparefoot\MyFootService\Models\BidIncreaseBannerValidation;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Dashboard Controller
 *
 * @copyright 2009 SpareFoot Inc
 * <AUTHOR> <PERSON>
 * Migrated to Symfony by Augment Agent
 */
class DashboardController extends AbstractRestrictedController
{
    /**
     * Initialize controller - migrated from _init()
     */
    protected function _init(Request $request): void
    {
        // Suppress unused parameter warning - parameter available for child controllers
        unset($request);

        if (!count($this->getLoggedUser()->getManagableFacilities())) {
            // TODO: Implement proper Symfony URL generation for features/add-first
            // This corresponds to the original: $this->redirect($this->view->url(['action' => 'add-first'], 'features'));
            throw new \Exception('Redirect to features/add-first needed - implement proper URL generation');
        }

        $this->view->hasOnlineMoveInFmsSoftware = Account::accountHasFmsSupportingOnlineMoveins($this->getLoggedUser()->getAccount());

        $this->view->banner = [
            'showMoveInsBanner' => \Genesis_Service_Feature::isActive(Features::MOVE_IN_BANNER, []),
            'showMoveInOnlineBanner' => \Genesis_Service_Feature::isActive(Features::MOVE_IN_ONLINE_BANNER, []),
            'showNotificationBanner' => BidIncreaseBannerValidation::isBidIncreaseBannerShown($this->getLoggedUser()->getAccount())
        ];
    }

    /**
     * Dashboard Index Action
     */
    #[Route('/dashboard', name: 'dashboard_index', methods: ['GET'])]
    public function index(): Response
    {
        $this->view->account = $this->getLoggedUser()->getAccount();

        if ($this->getSession()->facilityId) {
            $facility = \Genesis_Service_Facility::loadById($this->getSession()->facilityId);
            $this->view->facility = $facility;

            // Show welcome widget if facility is NOT reconciled
            $this->view->showWelcomeWidget = !Facility::hasReconciled($facility);
        }

        // Gets 4 most recent messages
        $this->view->messages = \Genesis_Service_AccountMgmtMessage::loadByNumber(4);

        // Set widget visibility flags based on feature flags
        $this->view->showSubmitRateWidget = User::isFeatureActive('myfoot.dashboard-widget.submitRate');
        $this->view->showMoveInRateWidget = User::isFeatureActive('myfoot.dashboard-widget.moveInRate');
        $this->view->showInventoryWidget = User::isFeatureActive('myfoot.dashboard-widget.inventory');
        $this->view->showCurrentBidWidget = User::isFeatureActive('myfoot.dashboard-widget.currentBidRanking');
        $this->view->showCustomerReviewsWidget = User::isFeatureActive('myfoot.dashboard-widget.customerReviews');

        $this->view->scripts = [
            'facility/global-functions',
            '../vendors/chartist/dist/chartist.min',
            '../sparefoot/plugins/current-bid-widget/script',
            '../sparefoot/plugins/customer-reviews-widget/script',
            '../sparefoot/plugins/inventory-widget/script',
            '../sparefoot/plugins/move-in-rate-chart/script',
            '../sparefoot/plugins/views-and-reservations-chart/script',
            'dashboard/index'
        ];

        $this->view->title = 'Dashboard';

        return $this->render('dashboard/index.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Get the current tab for navigation
     */
    protected function getTab(): string
    {
        return parent::TAB_HOME;
    }
}
